# 🔧 دليل الإعدادات المركزية

## نظرة عامة

تم توحيد جميع إعدادات التطبيق في ملف `.env` واحد لسهولة الإدارة والصيانة. الآن يمكنك تعديل جميع إعدادات التطبيق من مكان واحد فقط!

## 📁 هيكل الإعدادات الجديد

```
.env                          # ملف الإعدادات المركزي
├── إعدادات Odoo
├── إعدادات الاتصال والشبكة
├── إعدادات التطبيق الأساسية
├── رسائل الخطأ
├── الألوان والتدرجات
├── أبعاد التصميم
├── أحجام الخطوط
├── إعدادات الأمان
└── إعدادات الجلسات
```

## 🎯 الفوائد الرئيسية

### ✅ **قبل التحديث:**
- إعدادات مبعثرة في ملفات متعددة
- صعوبة في الصيانة
- تضارب في القيم
- تعديل في أماكن متعددة

### 🚀 **بعد التحديث:**
- **مكان واحد** لجميع الإعدادات
- **سهولة الصيانة** والتحديث
- **عدم تضارب** في القيم
- **تعديل سريع** من ملف واحد

## 📝 كيفية تعديل الإعدادات

### 1. **إعدادات الألوان:**
```env
# في ملف .env
PRIMARY_COLOR=3F91EB          # اللون الأساسي (بدون #)
SUCCESS_COLOR=28C76F          # لون النجاح
ERROR_COLOR=EA5455            # لون الخطأ
```

### 2. **إعدادات الأبعاد:**
```env
# في ملف .env
BORDER_RADIUS=16.0            # نصف قطر الحدود
BUTTON_HEIGHT=56.0            # ارتفاع الأزرار
SPACING=16.0                  # المسافات الأساسية
```

### 3. **إعدادات الخطوط:**
```env
# في ملف .env
HEADLINE_FONT_SIZE=28.0       # حجم خط العنوان
BODY_FONT_SIZE=16.0           # حجم خط النص
SMALL_FONT_SIZE=12.0          # حجم الخط الصغير
```

### 4. **إعدادات الأمان:**
```env
# في ملف .env
SESSION_TIMEOUT_MINUTES=2     # مدة الجلسة بالدقائق
CERTIFICATE_PINNING_ENABLED=true  # تفعيل Certificate Pinning
AUTO_LOGOUT_ENABLED=true      # تفعيل تسجيل الخروج التلقائي
```

## 🔄 كيفية عمل النظام

### 1. **قراءة الإعدادات:**
```dart
// EnvironmentService يقرأ من .env
final color = EnvironmentService.getPrimaryColor();
final timeout = EnvironmentService.getSessionTimeoutMinutes();
```

### 2. **استخدام الإعدادات:**
```dart
// AppConfig يستخدم EnvironmentService
final primaryColor = AppConfig.primaryColor;  // يقرأ من .env تلقائياً
final spacing = AppConfig.spacing;            // يقرأ من .env تلقائياً
```

### 3. **في واجهة المستخدم:**
```dart
// الاستخدام العادي كما هو
Container(
  color: AppConfig.primaryColorObj,           // Color object جاهز
  padding: EdgeInsets.all(AppConfig.spacing), // قيمة من .env
)
```

## 📋 قائمة الإعدادات المتاحة

### 🌐 **إعدادات الاتصال:**
- `CONNECTION_TIMEOUT` - مهلة الاتصال
- `REQUEST_TIMEOUT` - مهلة الطلب

### 📱 **إعدادات التطبيق:**
- `APP_NAME` - اسم التطبيق
- `APP_SUBTITLE` - العنوان الفرعي
- `APP_VERSION` - إصدار التطبيق

### 🎨 **الألوان:**
- `PRIMARY_COLOR` - اللون الأساسي
- `SUCCESS_COLOR` - لون النجاح
- `ERROR_COLOR` - لون الخطأ
- `WHITE_COLOR` - اللون الأبيض
- `DARK_TEXT_COLOR` - لون النص الداكن

### 📐 **الأبعاد:**
- `BORDER_RADIUS` - نصف قطر الحدود
- `BUTTON_HEIGHT` - ارتفاع الأزرار
- `SPACING` - المسافة الأساسية
- `LARGE_SPACING` - المسافة الكبيرة

### 🔤 **أحجام الخطوط:**
- `HEADLINE_FONT_SIZE` - حجم خط العنوان
- `TITLE_FONT_SIZE` - حجم خط العنوان الفرعي
- `BODY_FONT_SIZE` - حجم خط النص
- `SMALL_FONT_SIZE` - حجم الخط الصغير

### 🔒 **إعدادات الأمان:**
- `CERTIFICATE_PINNING_ENABLED` - تفعيل Certificate Pinning
- `SESSION_TIMEOUT_ENABLED` - تفعيل مهلة الجلسة
- `SESSION_TIMEOUT_MINUTES` - مدة الجلسة
- `AUTO_LOGOUT_ENABLED` - تفعيل تسجيل الخروج التلقائي

## 🚀 مثال عملي

### تغيير لون التطبيق:
```env
# في ملف .env - غيّر هذا السطر فقط
PRIMARY_COLOR=FF5722  # من الأزرق إلى البرتقالي
```

### تغيير مدة الجلسة:
```env
# في ملف .env - غيّر هذا السطر فقط
SESSION_TIMEOUT_MINUTES=5  # من دقيقتين إلى 5 دقائق
```

### تغيير أحجام الخطوط:
```env
# في ملف .env - غيّر هذه الأسطر
HEADLINE_FONT_SIZE=32.0  # خط أكبر للعناوين
BODY_FONT_SIZE=18.0      # خط أكبر للنص
```

## ⚠️ ملاحظات مهمة

1. **الألوان بصيغة hex بدون #**
2. **الأبعاد والخطوط بصيغة double**
3. **القيم المنطقية: true/false**
4. **إعادة تشغيل التطبيق** مطلوبة بعد تغيير الإعدادات

## 🔧 استكشاف الأخطاء

### إذا لم تظهر التغييرات:
1. تأكد من صحة صيغة القيم في `.env`
2. أعد تشغيل التطبيق
3. تحقق من عدم وجود أخطاء في وحدة التحكم

### إذا ظهرت قيم افتراضية:
- تأكد من وجود ملف `.env` في مجلد المشروع
- تحقق من صحة أسماء المتغيرات

## 🎉 الخلاصة

الآن يمكنك تخصيص التطبيق بالكامل من ملف `.env` واحد فقط! 
هذا يجعل الصيانة أسهل والتطوير أسرع. 🚀
