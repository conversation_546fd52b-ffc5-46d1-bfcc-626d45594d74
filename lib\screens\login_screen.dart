import 'package:flutter/material.dart';
import '../services/odoo_service.dart';
import '../services/storage_service.dart';
import '../services/biometric_service.dart';
import '../config/app_config.dart';
import '../generated/l10n/app_localizations.dart';
import 'employee_screen.dart';

/// شاشة تسجيل الدخول المصرفية الحديثة
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _rememberMe = false;
  bool _biometricEnabled = false;
  String? _errorMessage;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadSavedCredentials();
  }

  /// تحميل بيانات تسجيل الدخول المحفوظة
  Future<void> _loadSavedCredentials() async {
    try {
      final credentials = await StorageService.getLoginCredentials();

      if (credentials['rememberMe'] == 'true') {
        setState(() {
          _emailController.text = credentials['email'] ?? '';
          _passwordController.text = credentials['password'] ?? '';
          _rememberMe = true;
        });
      }

      // التحقق من توفر البصمة
      await _checkBiometricAvailability();
    } catch (e) {
      // في حالة حدوث خطأ، لا نفعل شيئاً
      debugPrint('خطأ في تحميل البيانات المحفوظة: $e');
    }
  }

  /// التحقق من توفر المصادقة البيومترية
  Future<void> _checkBiometricAvailability() async {
    try {
      final isEnabled = await BiometricService.isBiometricEnabled();

      setState(() {
        _biometricEnabled = isEnabled; // فصل البصمة عن تذكر البيانات
      });
    } catch (e) {
      setState(() {
        _biometricEnabled = false;
      });
    }
  }

  /// إزالة التركيز من جميع الحقول
  void _clearFocus() {
    _emailFocusNode.unfocus();
    _passwordFocusNode.unfocus();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  /// تسجيل الدخول باستخدام البصمة
  Future<void> _loginWithBiometric() async {
    // إزالة التركيز من الحقول
    _clearFocus();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await BiometricService.loginWithBiometric();

      if (result.success) {
        // إنشاء خدمة Odoo
        final odooService = OdooService(
          baseUrl: result.serverUrl ?? AppConfig.defaultServerUrl,
          database: result.database ?? AppConfig.defaultDatabase,
        );

        // محاولة المصادقة مع Odoo
        final uid = await odooService.authenticate(
          result.email!,
          result.password!,
        );

        if (uid != null) {
          // نجحت المصادقة - الانتقال إلى شاشة الموظف
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => EmployeeScreen(
                  odooService: odooService,
                  uid: uid,
                  password: result.password!,
                ),
              ),
            );
          }
        } else {
          setState(() {
            _errorMessage =
                'فشل في تسجيل الدخول. قد تكون البيانات المحفوظة غير صحيحة أو الخادم غير متاح.';
          });
        }
      } else {
        setState(() {
          _errorMessage = result.errorMessage ?? 'فشل في المصادقة البيومترية';
        });
      }
    } catch (e) {
      // معالجة أخطاء محددة لإظهار رسائل واضحة للمستخدم
      String userFriendlyMessage = 'حدث خطأ أثناء تسجيل الدخول';

      if (e.toString().contains('FormatException')) {
        userFriendlyMessage =
            'خطأ في الاتصال: الخادم لا يستجيب. تحقق من حالة الإنترنت.';
      } else if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('Connection failed')) {
        userFriendlyMessage = 'خطأ في الاتصال: لا يمكن الوصول إلى الخادم.';
      } else if (e.toString().contains('TimeoutException')) {
        userFriendlyMessage = 'خطأ في الاتصال: انتهت مهلة الاتصال.';
      } else {
        userFriendlyMessage = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
      }

      setState(() {
        _errorMessage = userFriendlyMessage;
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// دالة تسجيل الدخول
  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // إزالة التركيز من الحقول
    _clearFocus();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // إنشاء خدمة Odoo باستخدام القيم الافتراضية
      final odooService = OdooService(
        baseUrl: AppConfig.defaultServerUrl,
        database: AppConfig.defaultDatabase,
      );

      // محاولة المصادقة
      final uid = await odooService.authenticate(
        _emailController.text.trim(),
        _passwordController.text.trim(),
      );

      if (uid != null) {
        // إيقاف البصمة عند تسجيل الدخول بالطريقة العادية
        if (_biometricEnabled) {
          await BiometricService.disableBiometricAuth();
          setState(() {
            _biometricEnabled = false;
          });

          // إظهار رسالة إعلامية للمستخدم
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.fingerprint_outlined, color: Colors.white),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'تم إيقاف تسجيل الدخول بالبصمة تلقائياً',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
        }

        // حفظ بيانات تسجيل الدخول إذا اختار المستخدم ذلك
        await StorageService.saveLoginCredentials(
          email: _emailController.text.trim(),
          password: _passwordController.text.trim(),
          rememberMe: _rememberMe,
          serverUrl: AppConfig.defaultServerUrl,
          database: AppConfig.defaultDatabase,
        );

        // تم إزالة تنبيه تفعيل البصمة
        // if (_rememberMe && _biometricAvailable && !_biometricEnabled) {
        //   await _showBiometricSetupDialog();
        // }

        // نجحت المصادقة - الانتقال إلى شاشة الموظف
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => EmployeeScreen(
                odooService: odooService,
                uid: uid,
                password: _passwordController.text.trim(),
              ),
            ),
          );
        }
      } else {
        // فشلت المصادقة
        setState(() {
          _errorMessage =
              'فشل في تسجيل الدخول. تحقق من البريد الإلكتروني وكلمة المرور أو حالة الاتصال بالخادم.';
        });
      }
    } catch (e) {
      // معالجة أخطاء محددة لإظهار رسائل واضحة للمستخدم
      String userFriendlyMessage = 'حدث خطأ في الاتصال';

      if (e.toString().contains('FormatException')) {
        userFriendlyMessage =
            'خطأ في الاتصال: الخادم لا يستجيب. تحقق من عنوان الخادم وحالة الإنترنت.';
      } else if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('Connection failed')) {
        userFriendlyMessage =
            'خطأ في الاتصال: لا يمكن الوصول إلى الخادم. تحقق من عنوان الخادم وحالة الإنترنت.';
      } else if (e.toString().contains('TimeoutException')) {
        userFriendlyMessage =
            'خطأ في الاتصال: انتهت مهلة الاتصال. تحقق من سرعة الإنترنت وأعد المحاولة.';
      } else if (e.toString().contains('HandshakeException') ||
          e.toString().contains('TlsException')) {
        userFriendlyMessage = 'خطأ في الأمان: مشكلة في شهادة الأمان للخادم.';
      } else {
        userFriendlyMessage = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
      }

      setState(() {
        _errorMessage = userFriendlyMessage;
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // التعديل الرئيسي: تغيير لون الخلفية إلى تدرج أبيض وسماوي
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white, // اللون الأبيض في الأعلى
              const Color.fromARGB(255, 150, 201, 237), // اللون السماوي الفاتح
              const Color.fromARGB(
                255,
                247,
                248,
                248,
              ), // لون أبيض مائل للرمادي في الأسفل
            ],
            stops: const [0.3, 0.6, 0.9],
          ),
        ),
        child: SafeArea(
          child: GestureDetector(
            onTap: _clearFocus, // إزالة التركيز عند النقر خارج الحقول
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConfig.largeSpacing),
                  child: Column(
                    children: [
                      const SizedBox(height: AppConfig.spacing),
                      _buildHeader(),
                      const SizedBox(height: AppConfig.spacing),
                      _buildLoginCard(),
                      const SizedBox(height: AppConfig.smallSpacing),
                      _buildBottomImage(),
                      const SizedBox(height: AppConfig.smallSpacing),
                      _buildFooter(),
                      const SizedBox(height: AppConfig.smallSpacing),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء الصورة في الأعلى
  Widget _buildTopImage() {
    return Center(
      child: Image.asset(
        'assets/images/logos/app_logo.png',
        width: 300,
        height: 200,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(AppConfig.primaryColor), Color(0xFF2563EB)],
                begin: Alignment.bottomCenter,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: const Color(
                    AppConfig.primaryColor,
                  ).withValues(alpha: 0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: const Icon(
              Icons.account_balance,
              size: 60,
              color: Color(AppConfig.whiteColor),
            ),
          );
        },
      ),
    );
  }

  /// بناء الصورة في الأسفل
  Widget _buildBottomImage() {
    return Center(
      child: Image.asset(
        'assets/images/logos/app_logo_down.png',
        width: 200,
        height: 150,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 200,
            height: 100,
            decoration: BoxDecoration(
              color: const Color(0xFFF3F4F6),
              borderRadius: BorderRadius.circular(12),
            ),
          );
        },
      ),
    );
  }

  /// بناء رأس الصفحة مع شعار البنك
  Widget _buildHeader() {
    return Column(
      children: [
        // شعار البنك
        _buildTopImage(),
        const SizedBox(height: AppConfig.smallSpacing),

        // عنوان التطبيق
        Text(
          AppLocalizations.of(context).appName,
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: const Color(AppConfig.darkTextColor),
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة تسجيل الدخول
  Widget _buildLoginCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConfig.largeSpacing),
      decoration: BoxDecoration(
        color: const Color(AppConfig.whiteColor),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: const Color(AppConfig.cardShadowColor),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // حقل البريد الإلكتروني
            _buildEmailField(),
            const SizedBox(height: AppConfig.spacing),

            // حقل كلمة المرور
            _buildPasswordField(),
            const SizedBox(height: AppConfig.spacing),

            // رسالة الخطأ
            if (_errorMessage != null) _buildErrorMessage(),

            const SizedBox(height: AppConfig.spacing),

            // زر تسجيل الدخول
            _buildLoginButton(),

            // أيقونة تسجيل الدخول بالبصمة
            if (_biometricEnabled) ...[
              const SizedBox(height: AppConfig.smallSpacing),
              _buildBiometricLoginButton(),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء حقل البريد الإلكتروني
  Widget _buildEmailField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(
        //   'البريد الإلكتروني',
        //   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        //     fontWeight: FontWeight.w600,
        //     color: const Color(AppConfig.darkTextColor),
        //   ),
        // ),
        TextFormField(
          controller: _emailController,
          focusNode: _emailFocusNode,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          onFieldSubmitted: (_) {
            // الانتقال إلى حقل كلمة المرور عند الضغط على "التالي"
            FocusScope.of(context).requestFocus(_passwordFocusNode);
          },
          decoration: InputDecoration(
            hintText: AppLocalizations.of(context).enterEmail,
            prefixIcon: const Icon(
              Icons.email_outlined,
              color: Color(AppConfig.primaryColor),
            ),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال البريد الإلكتروني';
            }
            if (!value.contains('@')) {
              return 'يرجى إدخال بريد إلكتروني صحيح';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// بناء حقل كلمة المرور
  Widget _buildPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(
        //   'كلمة المرور',
        //   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        //     fontWeight: FontWeight.w600,
        //     color: const Color(AppConfig.darkTextColor),
        //   ),
        // ),
        TextFormField(
          controller: _passwordController,
          focusNode: _passwordFocusNode,
          obscureText: _obscurePassword,
          textInputAction: TextInputAction.done,
          onFieldSubmitted: (_) {
            // إزالة التركيز وتسجيل الدخول عند الضغط على "تم"
            _passwordFocusNode.unfocus();
            _login();
          },
          decoration: InputDecoration(
            hintText: AppLocalizations.of(context).enterPassword,
            prefixIcon: const Icon(
              Icons.lock_outlined,
              color: Color(AppConfig.primaryColor),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility_off : Icons.visibility,
                color: const Color(AppConfig.secondaryTextColor),
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال كلمة المرور';
            }
            if (value.length < 6) {
              return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
            }
            return null;
          },
        ),

        const SizedBox(height: AppConfig.smallSpacing),

        // خانة اختيار "تذكرني"
        _buildRememberMeCheckbox(),
      ],
    );
  }

  /// بناء خانة اختيار "تذكرني"
  Widget _buildRememberMeCheckbox() {
    return Row(
      children: [
        Transform.scale(
          scale: 1.2,
          child: Checkbox(
            value: _rememberMe,
            onChanged: (value) {
              setState(() {
                _rememberMe = value ?? false;
              });
            },
            activeColor: const Color(AppConfig.primaryColor),
            checkColor: const Color(AppConfig.whiteColor),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        const SizedBox(width: AppConfig.spacing),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _rememberMe = !_rememberMe;
              });
            },
            child: Text(
              'تذكر بيانات تسجيل الدخول',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: const Color(AppConfig.darkTextColor),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        // أيقونة معلومات
        GestureDetector(
          onTap: _showRememberMeInfo,
          child: Container(
            padding: const EdgeInsets.all(4),
            child: const Icon(
              Icons.info_outline,
              size: 18,
              color: Color(AppConfig.secondaryTextColor),
            ),
          ),
        ),
      ],
    );
  }

  /// عرض معلومات حول ميزة "تذكرني"
  void _showRememberMeInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
        title: const Row(
          children: [
            Icon(Icons.security, color: Color(AppConfig.primaryColor)),
            SizedBox(width: 8),
            Text('معلومات الأمان'),
          ],
        ),
        content: const Text(
          'عند تفعيل هذا الخيار، سيتم حفظ بيانات تسجيل الدخول بشكل آمن على جهازك لتسهيل الدخول في المرات القادمة.\n\n'
          'يمكنك إلغاء تفعيل هذا الخيار في أي وقت من إعدادات التطبيق.',
          style: TextStyle(fontSize: AppConfig.bodyFontSize, height: 1.5),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  /// بناء رسالة الخطأ
  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(AppConfig.spacing),
      decoration: BoxDecoration(
        color: const Color(AppConfig.errorColor).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(
          color: const Color(AppConfig.errorColor).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.error_outline,
            color: Color(AppConfig.errorColor),
            size: 20,
          ),
          const SizedBox(width: AppConfig.smallSpacing),
          Expanded(
            child: Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: const Color(AppConfig.errorColor),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر تسجيل الدخول
  Widget _buildLoginButton() {
    return Container(
      width: double.infinity,
      height: AppConfig.buttonHeight,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(AppConfig.primaryColor), Color(0xFF2563EB)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        boxShadow: [
          BoxShadow(
            color: const Color(AppConfig.primaryColor).withValues(alpha: 0.4),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _login,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Color(AppConfig.whiteColor),
                  strokeWidth: 2.5,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.login,
                    color: Color(AppConfig.whiteColor),
                    size: 22,
                  ),
                  const SizedBox(width: AppConfig.smallSpacing),
                  Text(
                    'تسجيل الدخول',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: const Color(AppConfig.whiteColor),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  /// بناء أيقونة تسجيل الدخول بالبصمة
  Widget _buildBiometricLoginButton() {
    return Center(
      child: Container(
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(AppConfig.successColor), Color(0xFF10B981)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: const Color(AppConfig.successColor).withValues(alpha: 0.4),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: Tooltip(
            message: 'تسجيل الدخول بالبصمة',
            child: InkWell(
              onTap: _isLoading ? null : _loginWithBiometric,
              borderRadius: BorderRadius.circular(35),
              child: _isLoading
                  ? const Center(
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Color(AppConfig.whiteColor),
                          strokeWidth: 2.5,
                        ),
                      ),
                    )
                  : const Center(
                      child: Icon(
                        Icons.fingerprint,
                        color: Color(AppConfig.whiteColor),
                        size: 32,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء تذييل الصفحة
  Widget _buildFooter() {
    return const Text(
      '©2025 IT Department Head Office LY',
      style: TextStyle(
        color: Color(0xFF6B7280),
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
      textAlign: TextAlign.center,
    );
  }
}
