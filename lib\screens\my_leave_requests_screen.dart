import 'package:flutter/material.dart';
import '../services/odoo_service.dart';
import '../models/leave_request.dart';
import '../config/app_config.dart';

/// شاشة عرض طلبات الإجازة الخاصة بالموظف
class MyLeaveRequestsScreen extends StatefulWidget {
  final OdooService odooService;
  final int uid;
  final String password;

  const MyLeaveRequestsScreen({
    super.key,
    required this.odooService,
    required this.uid,
    required this.password,
  });

  @override
  State<MyLeaveRequestsScreen> createState() => _MyLeaveRequestsScreenState();
}

class _MyLeaveRequestsScreenState extends State<MyLeaveRequestsScreen> {
  List<LeaveRequest> _leaveRequests = [];
  Map<String, double> _leaveBalances = {};
  bool _isLoading = true;
  bool _isLoadingBalance = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل جميع البيانات
  Future<void> _loadData() async {
    await Future.wait([_loadLeaveRequests(), _loadLeaveBalance()]);
  }

  /// تحميل طلبات الإجازة من Odoo
  Future<void> _loadLeaveRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final leaveRequestsData = await widget.odooService
          .getEmployeeLeaveRequests(uid: widget.uid, password: widget.password);

      if (leaveRequestsData != null) {
        try {
          setState(() {
            _leaveRequests = leaveRequestsData.map((data) {
              return LeaveRequest.fromOdooData(data);
            }).toList();
            _isLoading = false;
          });
        } catch (e) {
          setState(() {
            _errorMessage = 'خطأ في معالجة البيانات: $e';
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _errorMessage = 'لم يتم العثور على طلبات إجازة';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ في تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  /// تحميل رصيد الإجازات من Odoo
  Future<void> _loadLeaveBalance() async {
    setState(() {
      _isLoadingBalance = true;
    });

    try {
      final balanceData = await widget.odooService.getEmployeeLeaveBalance(
        uid: widget.uid,
        password: widget.password,
      );

      if (balanceData != null) {
        setState(() {
          _leaveBalances = balanceData;
          _isLoadingBalance = false;
        });
      } else {
        setState(() {
          _leaveBalances = {};
          _isLoadingBalance = false;
        });
      }
    } catch (e) {
      setState(() {
        _leaveBalances = {};
        _isLoadingBalance = false;
      });
    }
  }

  /// إعادة تحميل البيانات
  Future<void> _refreshData() async {
    await _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.lightGrayColorObj,
      appBar: _buildModernAppBar(context),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        color: AppConfig.primaryColorObj,
        backgroundColor: AppConfig.whiteColorObj,
        child: _buildBody(),
      ),
    );
  }

  /// بناء شريط التطبيق الحديث
  PreferredSizeWidget _buildModernAppBar(BuildContext context) {
    return AppBar(
      elevation: 0,
      backgroundColor: AppConfig.whiteColorObj,
      foregroundColor: AppConfig.darkTextColorObj,
      toolbarHeight: 0, // إخفاء شريط التطبيق بالكامل
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_leaveRequests.isEmpty) {
      return _buildEmptyState();
    }

    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        // عرض الرصيد المتاح
        SliverToBoxAdapter(child: _buildLeaveBalanceSection()),

        // إحصائيات سريعة
        SliverToBoxAdapter(child: _buildQuickStats()),

        // قائمة طلبات الإجازة
        SliverPadding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              final leaveRequest = _leaveRequests[index];
              return _buildModernLeaveRequestCard(leaveRequest, index);
            }, childCount: _leaveRequests.length),
          ),
        ),
      ],
    );
  }

  /// حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppConfig.whiteColorObj,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppConfig.cardShadowColorObj,
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                AppConfig.primaryColorObj,
              ),
            ),
          ),
          SizedBox(height: 24),
          Text(
            'جاري تحميل طلبات الإجازة...',
            style: TextStyle(
              fontSize: AppConfig.bodyFontSize,
              color: AppConfig.secondaryTextColorObj,
            ),
          ),
        ],
      ),
    );
  }

  /// حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: AppConfig.whiteColorObj,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: AppConfig.cardShadowColorObj,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppConfig.errorColorObj.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.error_outline_rounded,
                size: 48,
                color: AppConfig.errorColorObj,
              ),
            ),
            SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: AppConfig.bodyFontSize,
                color: AppConfig.darkTextColorObj,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _refreshData,
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConfig.primaryColorObj,
                  foregroundColor: AppConfig.whiteColorObj,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// حالة عدم وجود بيانات
  Widget _buildEmptyState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: AppConfig.whiteColorObj,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: AppConfig.cardShadowColorObj,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(
                  AppConfig.primaryColor,
                ).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.event_busy_rounded,
                size: 64,
                color: AppConfig.primaryColorObj,
              ),
            ),
            SizedBox(height: 20),
            Text(
              'لا توجد طلبات إجازة',
              style: TextStyle(
                fontSize: AppConfig.titleFontSize,
                fontWeight: FontWeight.bold,
                color: AppConfig.darkTextColorObj,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم تقم بتقديم أي طلبات إجازة حتى الآن',
              style: TextStyle(
                fontSize: AppConfig.bodyFontSize,
                color: AppConfig.secondaryTextColorObj,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء إحصائيات سريعة
  Widget _buildQuickStats() {
    if (_leaveRequests.isEmpty) return const SizedBox.shrink();

    final approvedRequests = _leaveRequests
        .where((r) => r.state == 'validate')
        .length;
    final pendingRequests = _leaveRequests
        .where((r) => r.state == 'confirm')
        .length;
    final totalDays = _leaveRequests.fold<double>(
      0,
      (sum, r) => sum + r.numberOfDays,
    );

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              title: 'المعتمدة',
              value: approvedRequests.toString(),
              icon: Icons.check_circle_outline,
              color: AppConfig.successColorObj,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              title: 'قيد المراجعة',
              value: pendingRequests.toString(),
              icon: Icons.schedule_outlined,
              color: AppConfig.primaryColorObj,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              title: 'إجمالي الأيام',
              value: totalDays.toStringAsFixed(0),
              icon: Icons.calendar_month_outlined,
              color: AppConfig.secondaryTextColorObj,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppConfig.whiteColorObj,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppConfig.cardShadowColorObj,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: AppConfig.secondaryTextColorObj,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة طلب إجازة حديثة
  Widget _buildModernLeaveRequestCard(LeaveRequest leaveRequest, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _showLeaveRequestDetails(leaveRequest),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppConfig.whiteColorObj,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppConfig.cardShadowColorObj,
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس البطاقة
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Color(
                          leaveRequest.stateColor,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        _getLeaveTypeIcon(leaveRequest.holidayStatusName),
                        color: Color(leaveRequest.stateColor),
                        size: 24,
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            leaveRequest.holidayStatusName,
                            style: TextStyle(
                              fontSize: AppConfig.titleFontSize,
                              fontWeight: FontWeight.bold,
                              color: AppConfig.darkTextColorObj,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Color(leaveRequest.stateColor),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              leaveRequest.stateText,
                              style: TextStyle(
                                color: AppConfig.whiteColorObj,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // تفاصيل التواريخ
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppConfig.lightGrayColorObj,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: _buildModernDetailItem(
                          icon: Icons.play_arrow_rounded,
                          label: 'تاريخ البداية',
                          value: _formatDate(leaveRequest.dateFrom),
                          color: AppConfig.successColorObj,
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 40,
                        color: AppConfig.dividerColorObj,
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                      ),
                      Expanded(
                        child: _buildModernDetailItem(
                          icon: Icons.stop_rounded,
                          label: 'تاريخ النهاية',
                          value: _formatDate(leaveRequest.dateTo),
                          color: AppConfig.errorColorObj,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // عدد الأيام
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(
                          AppConfig.primaryColor,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.schedule_rounded,
                        color: AppConfig.primaryColorObj,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'المدة: ${leaveRequest.numberOfDays.toStringAsFixed(1)} يوم',
                      style: TextStyle(
                        fontSize: AppConfig.bodyFontSize,
                        fontWeight: FontWeight.w600,
                        color: AppConfig.darkTextColorObj,
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      size: 16,
                      color: AppConfig.secondaryTextColorObj,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء عنصر تفصيلي حديث
  Widget _buildModernDetailItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, size: 20, color: color),
        SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppConfig.secondaryTextColorObj,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppConfig.darkTextColorObj,
          ),
        ),
      ],
    );
  }

  /// الحصول على أيقونة نوع الإجازة
  IconData _getLeaveTypeIcon(String leaveType) {
    final type = leaveType.toLowerCase();
    if (type.contains('سنوي') || type.contains('annual')) {
      return Icons.beach_access_rounded;
    } else if (type.contains('مرضي') || type.contains('sick')) {
      return Icons.local_hospital_rounded;
    } else if (type.contains('طارئ') || type.contains('emergency')) {
      return Icons.emergency_rounded;
    } else if (type.contains('أمومة') || type.contains('maternity')) {
      return Icons.child_care_rounded;
    } else if (type.contains('أبوة') || type.contains('paternity')) {
      return Icons.family_restroom_rounded;
    } else if (type.contains('دراسة') || type.contains('study')) {
      return Icons.school_rounded;
    } else {
      return Icons.event_available_rounded;
    }
  }

  /// عرض تفاصيل طلب الإجازة
  void _showLeaveRequestDetails(LeaveRequest leaveRequest) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: AppConfig.whiteColorObj,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppConfig.dividerColorObj,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // محتوى التفاصيل
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // العنوان
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Color(
                              leaveRequest.stateColor,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            _getLeaveTypeIcon(leaveRequest.holidayStatusName),
                            color: Color(leaveRequest.stateColor),
                            size: 24,
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'تفاصيل الطلب',
                                style: TextStyle(
                                  fontSize: AppConfig.titleFontSize,
                                  fontWeight: FontWeight.bold,
                                  color: AppConfig.darkTextColorObj,
                                ),
                              ),
                              Text(
                                leaveRequest.holidayStatusName,
                                style: TextStyle(
                                  fontSize: AppConfig.bodyFontSize,
                                  color: AppConfig.secondaryTextColorObj,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // تفاصيل الطلب
                    _buildDetailRow('الحالة', leaveRequest.stateText),
                    _buildDetailRow(
                      'تاريخ البداية',
                      _formatDate(leaveRequest.dateFrom),
                    ),
                    _buildDetailRow(
                      'تاريخ النهاية',
                      _formatDate(leaveRequest.dateTo),
                    ),
                    _buildDetailRow(
                      'عدد الأيام',
                      '${leaveRequest.numberOfDays.toStringAsFixed(1)} يوم',
                    ),

                    const Spacer(),

                    // زر الإغلاق
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('إغلاق'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صف تفصيلي
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: AppConfig.bodyFontSize,
                color: AppConfig.secondaryTextColorObj,
              ),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: AppConfig.bodyFontSize,
                fontWeight: FontWeight.w600,
                color: AppConfig.darkTextColorObj,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم عرض الرصيد المتاح
  Widget _buildLeaveBalanceSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [AppConfig.primaryColorObj, Color(0xFF2563EB)],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: AppConfig.primaryColorObj.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس القسم
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(
                        AppConfig.whiteColor,
                      ).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      Icons.account_balance_wallet_rounded,
                      color: AppConfig.whiteColorObj,
                      size: 24,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'رصيد الإجازات',
                          style: TextStyle(
                            fontSize: AppConfig.titleFontSize,
                            fontWeight: FontWeight.bold,
                            color: AppConfig.whiteColorObj,
                          ),
                        ),
                        Text(
                          'الأيام المتاحة لكل نوع إجازة',
                          style: TextStyle(
                            fontSize: AppConfig.captionFontSize,
                            color: AppConfig.whiteColorObj,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // محتوى الرصيد
              if (_isLoadingBalance)
                _buildLoadingBalanceContent()
              else if (_leaveBalances.isEmpty)
                _buildEmptyBalanceContent()
              else
                _buildBalanceContent(),
            ],
          ),
        ),
      ),
    );
  }

  /// محتوى تحميل الرصيد
  Widget _buildLoadingBalanceContent() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppConfig.whiteColorObj.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                AppConfig.whiteColorObj,
              ),
            ),
          ),
          SizedBox(width: 16),
          Text(
            'جاري تحميل الرصيد المتاح...',
            style: TextStyle(
              color: AppConfig.whiteColorObj,
              fontSize: AppConfig.bodyFontSize,
            ),
          ),
        ],
      ),
    );
  }

  /// محتوى عدم وجود رصيد
  Widget _buildEmptyBalanceContent() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppConfig.whiteColorObj.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline_rounded,
            color: AppConfig.whiteColorObj,
            size: 20,
          ),
          SizedBox(width: 16),
          Text(
            'لا يوجد رصيد إجازات متاح',
            style: TextStyle(
              color: AppConfig.whiteColorObj,
              fontSize: AppConfig.bodyFontSize,
            ),
          ),
        ],
      ),
    );
  }

  /// محتوى الرصيد
  Widget _buildBalanceContent() {
    return Column(
      children: _leaveBalances.entries.map((entry) {
        return _buildModernBalanceItem(entry.key, entry.value);
      }).toList(),
    );
  }

  /// بناء عنصر رصيد حديث
  Widget _buildModernBalanceItem(String leaveType, double balance) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppConfig.whiteColorObj.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppConfig.whiteColorObj.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppConfig.whiteColorObj.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              _getLeaveTypeIcon(leaveType),
              color: AppConfig.whiteColorObj,
              size: 20,
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  leaveType,
                  style: TextStyle(
                    fontSize: AppConfig.bodyFontSize,
                    fontWeight: FontWeight.w600,
                    color: AppConfig.whiteColorObj,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'متاح',
                  style: TextStyle(
                    fontSize: AppConfig.captionFontSize,
                    color: const Color(
                      AppConfig.whiteColor,
                    ).withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppConfig.whiteColorObj,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${balance.toStringAsFixed(1)} يوم',
              style: TextStyle(
                fontSize: AppConfig.bodyFontSize,
                fontWeight: FontWeight.bold,
                color: AppConfig.primaryColorObj,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
